<!-- Modern Interactive Library Management Dashboard -->
<div class="min-h-screen transition-colors duration-300 flex" [class]="isDarkMode ? 'bg-gray-900' : 'bg-gray-50'" [attr.data-theme]="isDarkMode ? 'dark' : 'light'">
  <!-- Mobile Menu Button -->
  <button
    (click)="toggleMobileMenu()"
    class="lg:hidden fixed top-1 left-1 z-50 p-1 rounded transition-all duration-200 mobile-menu-btn shadow-sm"
    [class]="isDarkMode ? 'bg-gray-800 text-white hover:bg-gray-700 border border-gray-700' : 'bg-white text-gray-800 hover:bg-gray-100 border border-gray-200'"
  >
    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
    </svg>
  </button>

  <!-- Left Navigation Pane (Fixed) -->
  <aside
    class="w-64 h-screen shadow-lg flex-shrink-0 border-r flex flex-col transition-all duration-300"
    [class]="getAsideClasses()"
    [style.transform]="isMobileMenuOpen ? 'translateX(0)' : ''"
  >
    <!-- Header -->
    <div class="p-6 border-b transition-colors duration-300 flex-shrink-0" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
      <div class="flex items-center justify-center">
        <img
          src="assets/images/BcLogo.png"
          alt="Benedicto College Logo"
          class="h-12 w-auto object-contain"
        >
      </div>
    </div>

    <!-- Navigation Links -->
    <nav class="flex-1 p-4 overflow-y-auto">
      <div class="space-y-1">
        <!-- Main Dashboard -->
        <a (click)="onNavigate('overview'); closeMobileMenu()"
           class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer"
           [class]="isSectionActive('overview') ?
             (isDarkMode ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-700') :
             (isDarkMode ? 'text-gray-300 hover:bg-blue-500 hover:text-white' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700')">
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
          </svg>
          Overview
        </a>

        <!-- Library Management Section -->
        <div class="pt-4 pb-2">
          <button (click)="toggleLibraryManagement()" class="nav-toggle-btn w-full flex items-center justify-between px-4 py-2 text-xs font-semibold uppercase tracking-wider" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">
            <span>Library Management</span>
            <svg class="nav-arrow w-4 h-4" [class.rotated]="isLibraryManagementCollapsed" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
        </div>

        <div class="nav-section space-y-1 mb-4" [class.collapsed]="isLibraryManagementCollapsed" [class.expanded]="!isLibraryManagementCollapsed">
          <a (click)="onNavigate('books'); closeMobileMenu()"
             class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer"
             [class]="isSectionActive('books') ?
               (isDarkMode ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-700') :
               (isDarkMode ? 'text-gray-300 hover:bg-blue-500 hover:text-white' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700')">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
            Books & Catalog
          </a>

          <a (click)="onNavigate('borrowing'); closeMobileMenu()"
             class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer"
             [class]="isSectionActive('borrowing') ?
               (isDarkMode ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-700') :
               (isDarkMode ? 'text-gray-300 hover:bg-blue-500 hover:text-white' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700')">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            Borrowing & Returns
          </a>

          <a (click)="onNavigate('reservations'); closeMobileMenu()"
             class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer"
             [class]="isSectionActive('reservations') ?
               (isDarkMode ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-700') :
               (isDarkMode ? 'text-gray-300 hover:bg-blue-500 hover:text-white' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700')">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Reservations
          </a>
        </div>

        <!-- User Management Section -->
        <div class="pt-4 pb-2">
          <button (click)="toggleUserManagement()" class="nav-toggle-btn w-full flex items-center justify-between px-4 py-2 text-xs font-semibold uppercase tracking-wider" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">
            <span>User Management</span>
            <svg class="nav-arrow w-4 h-4" [class.rotated]="isUserManagementCollapsed" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
        </div>

        <div class="nav-section space-y-1 mb-4" [class.collapsed]="isUserManagementCollapsed" [class.expanded]="!isUserManagementCollapsed">
          <a (click)="onNavigate('students'); closeMobileMenu()"
             class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer"
             [class]="isSectionActive('students') ?
               (isDarkMode ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-700') :
               (isDarkMode ? 'text-gray-300 hover:bg-blue-500 hover:text-white' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700')">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
            </svg>
            Students
          </a>

          <a (click)="onNavigate('faculty'); closeMobileMenu()"
             class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer"
             [class]="isSectionActive('faculty') ?
               (isDarkMode ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-700') :
               (isDarkMode ? 'text-gray-300 hover:bg-blue-500 hover:text-white' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700')">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            Faculty
          </a>
        </div>

        <!-- Super Admin Only Section -->
        <div *ngIf="isSuperAdmin()" class="pt-4 pb-2">
          <button (click)="toggleSystemAdmin()" class="nav-toggle-btn w-full flex items-center justify-between px-4 py-2 text-xs font-semibold uppercase tracking-wider" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">
            <span>System Administration</span>
            <svg class="nav-arrow w-4 h-4" [class.rotated]="isSystemAdminCollapsed" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
        </div>

        <div *ngIf="isSuperAdmin()" class="nav-section space-y-1 mb-4" [class.collapsed]="isSystemAdminCollapsed" [class.expanded]="!isSystemAdminCollapsed">
          <a (click)="onNavigate('admins'); closeMobileMenu()"
             class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer"
             [class]="isSectionActive('admins') ?
               (isDarkMode ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-700') :
               (isDarkMode ? 'text-gray-300 hover:bg-blue-500 hover:text-white' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700')">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            Admin Management
          </a>

          <a (click)="onNavigate('system-settings'); closeMobileMenu()"
             class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer"
             [class]="isSectionActive('system-settings') ?
               (isDarkMode ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-700') :
               (isDarkMode ? 'text-gray-300 hover:bg-blue-500 hover:text-white' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700')">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            System Settings
          </a>
        </div>

        <!-- Reports & Analytics Section -->
        <div class="pt-4 pb-2">
          <button (click)="toggleReports()" class="nav-toggle-btn w-full flex items-center justify-between px-4 py-2 text-xs font-semibold uppercase tracking-wider" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">
            <span>Reports & Analytics</span>
            <svg class="nav-arrow w-4 h-4" [class.rotated]="isReportsCollapsed" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
        </div>

        <div class="nav-section space-y-1 mb-4" [class.collapsed]="isReportsCollapsed" [class.expanded]="!isReportsCollapsed">
          <a (click)="onNavigate('reports'); closeMobileMenu()"
             class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer"
             [class]="isSectionActive('reports') ?
               (isDarkMode ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-700') :
               (isDarkMode ? 'text-gray-300 hover:bg-blue-500 hover:text-white' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700')">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Analytics
          </a>

          <a (click)="onNavigate('logs'); closeMobileMenu()"
             class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer"
             [class]="isSectionActive('logs') ?
               (isDarkMode ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-700') :
               (isDarkMode ? 'text-gray-300 hover:bg-blue-500 hover:text-white' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700')">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Activity Logs
          </a>
        </div>
      </div>
    </nav>

    <!-- Logout Button -->
    <div class="p-4 border-t transition-colors duration-300 flex-shrink-0" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
      <button (click)="onLogout()" class="logout-btn w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013 3v1"></path>
        </svg>
        Logout
      </button>
    </div>
  </aside>

  <!-- Main Content Area -->
  <div class="flex-1 flex">
    <!-- Dashboard Content -->
    <div class="flex-1 flex flex-col">
      <!-- Top Header -->
      <header [class]="getHeaderClasses()" class="dashboard-header">
        <div class="flex justify-between items-center min-h-[48px] lg:min-h-[64px] px-1 lg:px-6">
          <div class="flex-1 min-w-0 pr-1 lg:pr-4">
            <div class="flex flex-col space-y-0.5">
              <h2 class="text-xs sm:text-sm lg:text-2xl font-medium lg:font-bold mobile-greeting" [class]="getTextClasses()">
                {{ getMobileGreeting() }}
              </h2>
              <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-2 text-xs lg:text-sm" [class]="getSecondaryTextClasses()">
                <span class="mobile-role font-medium">{{ getCurrentAdmin()?.role || 'Super Admin' }}</span>
                <!-- Date only visible on mobile/tablet, hidden on desktop -->
                <span class="mobile-date lg:hidden">{{ getMobileDate() }}</span>
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2 lg:space-x-4 flex-shrink-0">
            <!-- Dark Mode Toggle -->
            <button
              (click)="toggleDarkMode()"
              class="p-2 rounded-lg transition-colors duration-200"
              [class]="isDarkMode ? 'text-yellow-400 hover:text-yellow-300' : 'text-gray-600 hover:text-gray-900'"
              title="Toggle Dark Mode"
            >
              <svg *ngIf="!isDarkMode" class="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
              </svg>
              <svg *ngIf="isDarkMode" class="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            </button>
            <!-- Notification Bell -->
            <button
              (click)="onNotificationClick()"
              class="relative p-2 transition-colors duration-200"
              [class]="isDarkMode ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'"
            >
              <svg class="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
              </svg>
              <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white notification-badge"></span>
            </button>
            <!-- User Profile -->
            <div class="relative" #profileButton>
              <button
                (click)="onProfileClick()"
                class="flex items-center space-x-2 p-2 rounded-lg transition-colors duration-200"
                [class]="isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'"
              >
                <img class="w-7 h-7 lg:w-8 lg:h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Crect width='32' height='32' fill='%233B82F6'/%3E%3Ctext x='16' y='20' text-anchor='middle' fill='white' font-family='Arial' font-size='14' font-weight='bold'%3EA%3C/text%3E%3C/svg%3E" alt="Admin">
              </button>
            </div>


          </div>
        </div>
      </header>

      <!-- Main Dashboard Content -->
      <main class="flex-1 p-3 lg:p-6 overflow-y-auto transition-colors duration-300 dashboard-main-content" [class]="getMainContentClasses()">
        <!-- Router Outlet for Dynamic Content -->
        <router-outlet></router-outlet>
      </main>
    </div>

    <!-- Right Sidebar -->
    <aside class="shadow-lg border-l flex-shrink-0 transition-all duration-300"
           [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'"
           [ngClass]="{'w-80': !isSidebarHidden, 'w-0 overflow-hidden': isSidebarHidden}">
      <div class="p-6 space-y-6">
        <!-- Date Widget -->
        <div class="border rounded-lg p-4 transition-colors duration-300" [class]="getCardClasses()">
          <div class="flex items-center mb-2">
            <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <h4 class="text-lg font-semibold" [class]="getTextClasses()">Today</h4>
          </div>
          <p class="text-xl font-bold" [class]="getTextClasses()">{{ getCurrentDate() }}</p>
        </div>

        <!-- Weather Widget -->
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-lg font-semibold mb-1">Weather</h4>
              <p class="text-3xl font-bold" id="temperature">32°C</p>
              <p class="text-blue-100" id="location">Cebu City</p>
            </div>
            <div class="text-right">
              <svg class="w-12 h-12 text-yellow-300" fill="currentColor" viewBox="0 0 24 24" id="weather-icon">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
          </div>
        </div>

        <!-- Horoscope Widget -->
        <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
          <div class="flex items-center mb-3">
            <svg class="w-6 h-6 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <h4 class="text-lg font-semibold" [class]="getTextClasses()">Horoscope</h4>
          </div>
          <p [class]="getSecondaryTextClasses()"><strong>Cancer:</strong> Study focus brings good results.</p>
        </div>

        <!-- Calendar Events Widget -->
        <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
          <div class="flex items-center mb-4">
            <svg class="w-6 h-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <h4 class="text-lg font-semibold" [class]="getTextClasses()">Upcoming Events</h4>
          </div>
          <div class="space-y-3">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <div>
                <p class="font-medium" [class]="getTextClasses()">July 10 – Book Fair</p>
                <p class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">3 days remaining</p>
              </div>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 text-orange-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <div>
                <p class="font-medium" [class]="getTextClasses()">July 15 – Research Submission</p>
                <p class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">8 days remaining</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Quote of the Day Widget -->
        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-6 text-white">
          <div class="flex items-center mb-3">
            <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
            </svg>
            <h4 class="text-lg font-semibold">Quote of the Day</h4>
          </div>
          <p class="text-purple-100 italic">"A room without books is like a body without a soul."</p>
        </div>


      </div>
    </aside>
  </div>

  <!-- Mobile Menu Overlay -->
  <div
    *ngIf="isMobileMenuOpen"
    class="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
    (click)="closeMobileMenu()"
  ></div>

  <!-- AI Chat Assistant Widget -->
  <div class="fixed bottom-6 right-6 z-50">
    <!-- Tooltip -->
    <div
      *ngIf="!isChatOpen && showTooltip"
      class="absolute bottom-16 right-0 mb-2 px-4 py-2 bg-white rounded-lg shadow-lg border border-gray-200 text-sm text-gray-700 whitespace-nowrap transform transition-all duration-300 ease-in-out"
      [class]="isDarkMode ? 'bg-gray-800 border-gray-600 text-gray-200' : 'bg-white border-gray-200 text-gray-700'"
    >
      <div class="relative">
        Need help finding books?
        <!-- Tooltip arrow -->
        <div class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white"
             [class]="isDarkMode ? 'border-t-gray-800' : 'border-t-white'"></div>
      </div>
    </div>

    <!-- Chat Window (Expanded) -->
    <div
      *ngIf="isChatOpen"
      class="bg-white rounded-2xl shadow-2xl border border-gray-200 w-80 h-96 flex flex-col transform transition-all duration-300 ease-in-out origin-bottom-right"
      [class]="isDarkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'"
    >
      <!-- Chat Header -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200 rounded-t-2xl"
           [class]="isDarkMode ? 'border-gray-600 bg-gray-750' : 'border-gray-200 bg-gray-50'">
        <div class="flex items-center space-x-3">
          <div class="relative">
            <img
              src="assets/images/bc-ai-avatar.svg"
              alt="BC-AI"
              class="w-8 h-8 rounded-full bg-blue-500 object-cover"
              (error)="onAvatarError($event)"
            >
            <!-- Online indicator -->
            <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"
                 [class]="isDarkMode ? 'border-gray-800' : 'border-white'"></div>
          </div>
          <div>
            <h3 class="font-semibold text-sm" [class]="getTextClasses()">BC-AI</h3>
            <p class="text-xs" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">Online</p>
          </div>
        </div>
        <button
          (click)="toggleChat()"
          class="p-1 rounded-full hover:bg-gray-100 transition-colors duration-200"
          [class]="isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-500'"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Chat Messages Area -->
      <div class="flex-1 p-4 overflow-y-auto space-y-3" #chatMessagesContainer>
        <!-- Welcome Message -->
        <div class="flex items-start space-x-2">
          <img
            src="assets/images/bc-ai-avatar.svg"
            alt="BC-AI"
            class="w-6 h-6 rounded-full bg-blue-500 flex-shrink-0 mt-1"
            (error)="onAvatarError($event)"
          >
          <div class="bg-gray-100 rounded-lg px-3 py-2 max-w-xs"
               [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-100'">
            <p class="text-sm" [class]="getTextClasses()">
              Hi! I'm BC-AI, your library assistant. How can I help you find books today?
            </p>
          </div>
        </div>

        <!-- Sample conversation -->
        <div *ngFor="let message of chatMessages" class="flex" [class.justify-end]="message.isUser">
          <div class="flex items-start space-x-2" [class.flex-row-reverse]="message.isUser" [class.space-x-reverse]="message.isUser">
            <img
              *ngIf="!message.isUser"
              src="assets/images/bc-ai-avatar.svg"
              alt="BC-AI"
              class="w-6 h-6 rounded-full bg-blue-500 flex-shrink-0 mt-1"
              (error)="onAvatarError($event)"
            >
            <div
              class="rounded-lg px-3 py-2 max-w-xs"
              [class]="message.isUser ?
                'bg-blue-500 text-white' :
                (isDarkMode ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-900')"
            >
              <p class="text-sm">{{ message.text }}</p>
              <p class="text-xs mt-1 opacity-70">{{ message.time }}</p>
            </div>
          </div>
        </div>

        <!-- Typing indicator -->
        <div *ngIf="isTyping" class="flex items-start space-x-2">
          <img
            src="assets/images/bc-ai-avatar.svg"
            alt="BC-AI"
            class="w-6 h-6 rounded-full bg-blue-500 flex-shrink-0 mt-1"
            (error)="onAvatarError($event)"
          >
          <div class="bg-gray-100 rounded-lg px-3 py-2"
               [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-100'">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chat Input -->
      <div class="p-4 border-t border-gray-200"
           [class]="isDarkMode ? 'border-gray-600' : 'border-gray-200'">
        <div class="flex space-x-2">
          <input
            type="text"
            [(ngModel)]="chatInput"
            (keyup.enter)="sendMessage()"
            placeholder="Type your message..."
            class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'"
          >
          <button
            (click)="sendMessage()"
            [disabled]="!chatInput.trim()"
            class="px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Floating Chat Button (Collapsed) -->
    <button
      *ngIf="!isChatOpen"
      (click)="toggleChat()"
      (mouseenter)="showTooltip = true"
      (mouseleave)="showTooltip = false"
      class="w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-in-out flex items-center justify-center relative overflow-hidden"
    >
      <!-- Pulse animation -->
      <div class="absolute inset-0 bg-blue-400 rounded-full animate-ping opacity-20"></div>

      <!-- Avatar or Chat Icon -->
      <img
        src="assets/images/bc-ai-avatar.svg"
        alt="BC-AI"
        class="w-8 h-8 rounded-full object-cover relative z-10"
        (error)="onAvatarError($event)"
      >

      <!-- Fallback icon if image fails -->
      <svg
        *ngIf="avatarError"
        class="w-6 h-6 relative z-10"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
      </svg>

      <!-- Notification badge -->
      <div *ngIf="hasUnreadMessages" class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
        {{ unreadCount }}
      </div>
    </button>
  </div>
</div>

<!-- Logout Confirmation Modal -->
<div
  *ngIf="showLogoutModal"
  class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm"
  (click)="cancelLogout()"
>
  <div
    class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 scale-100"
    [class]="isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'"
    (click)="$event.stopPropagation()"
  >
    <!-- Modal Header -->
    <div class="p-6 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <h3 class="text-lg font-semibold" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">
            Confirm Logout
          </h3>
          <p class="text-sm" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-600'">
            You are about to sign out of your admin session
          </p>
        </div>
      </div>
    </div>

    <!-- Modal Body -->
    <div class="p-6">
      <p class="text-sm" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-600'">
        Are you sure you want to log out? You will need to sign in again to access the admin dashboard.
      </p>
    </div>

    <!-- Modal Footer -->
    <div class="p-6 border-t flex flex-col sm:flex-row gap-3 sm:gap-4" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
      <!-- Cancel Button -->
      <button
        type="button"
        (click)="cancelLogout()"
        class="flex-1 px-4 py-2.5 text-sm font-medium rounded-lg border transition-colors duration-200"
        [class]="isDarkMode ?
          'border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500' :
          'border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400'"
      >
        <span class="flex items-center justify-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
          No, Stay Logged In
        </span>
      </button>

      <!-- Confirm Button -->
      <button
        type="button"
        (click)="confirmLogout()"
        class="flex-1 px-4 py-2.5 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-4 focus:ring-red-200 transition-colors duration-200"
      >
        <span class="flex items-center justify-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
          </svg>
          Yes, Log Out
        </span>
      </button>
    </div>
  </div>
</div>

<!-- Profile Modal -->
<div
  *ngIf="showProfileModal"
  class="profile-modal fixed w-48 rounded-lg shadow-2xl border-2 transform transition-all duration-200 ease-out"
  [class]="isDarkMode ? 'bg-gray-800 border-gray-500 shadow-gray-900/50' : 'bg-white border-blue-200 shadow-blue-500/20'"
  (click)="$event.stopPropagation()"
  style="z-index: 10000 !important; top: 70px; right: 280px;"
>
  <div class="py-2">
    <button
      (click)="viewProfile()"
      class="w-full text-left px-4 py-3 text-sm font-medium transition-colors duration-200 flex items-center space-x-3 rounded-md mx-2"
      [class]="isDarkMode ? 'text-white hover:bg-blue-600 hover:text-white' : 'text-gray-800 hover:bg-blue-50 hover:text-blue-700'"
    >
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
      </svg>
      <span>View Profile</span>
    </button>
  </div>
</div>

<!-- Profile Modal Overlay -->
<div
  *ngIf="showProfileModal"
  class="profile-modal-overlay fixed inset-0"
  (click)="closeProfileModal()"
></div>
