import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-books',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './books.component.html',
  styleUrls: ['./books.component.css']
})
export class BooksComponent implements OnInit {
  isDarkMode: boolean = false;

  books = [
    {
      id: 1,
      title: 'The Great Gatsby',
      author: '<PERSON><PERSON>',
      isbn: '978-0-7432-7356-5',
      category: 'Fiction',
      status: 'Available',
      location: 'A-1-001'
    },
    {
      id: 2,
      title: 'To Kill a Mockingbird',
      author: '<PERSON> Lee',
      isbn: '978-0-06-112008-4',
      category: 'Fiction',
      status: 'Borrowed',
      location: 'A-1-002'
    },
    {
      id: 3,
      title: 'Introduction to Algorithms',
      author: '<PERSON>',
      isbn: '978-0-262-03384-8',
      category: 'Technology',
      status: 'Available',
      location: 'T-2-045'
    }
  ];

  constructor() { }

  ngOnInit(): void {
    // Check for dark mode preference
    this.isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
  }

  addNewBook(): void {
    console.log('Add new book clicked');
  }

  editBook(bookId: number): void {
    console.log('Edit book:', bookId);
  }

  viewBook(bookId: number): void {
    console.log('View book:', bookId);
  }

  deleteBook(bookId: number): void {
    console.log('Delete book:', bookId);
  }
}
