import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-students',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './students.component.html',
  styleUrls: ['./students.component.css']
})
export class StudentsComponent implements OnInit {
  isDarkMode: boolean = false;

  students = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      studentId: '2021-00123',
      program: 'Computer Science',
      yearLevel: '3rd Year',
      status: 'Active',
      borrowedBooks: 2
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      studentId: '2020-00456',
      program: 'Engineering',
      yearLevel: '4th Year',
      status: 'Active',
      borrowedBooks: 1
    },
    {
      id: 3,
      name: '<PERSON>',
      email: '<EMAIL>',
      studentId: '2022-00789',
      program: 'Business',
      yearLevel: '2nd Year',
      status: 'Suspended',
      borrowedBooks: 0
    }
  ];

  constructor() { }

  ngOnInit(): void {
    // Check for dark mode preference
    this.isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
  }

  addNewStudent(): void {
    console.log('Add new student clicked');
  }

  editStudent(studentId: number): void {
    console.log('Edit student:', studentId);
  }

  viewStudent(studentId: number): void {
    console.log('View student:', studentId);
  }

  suspendStudent(studentId: number): void {
    console.log('Suspend student:', studentId);
  }

  getInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  }
}
