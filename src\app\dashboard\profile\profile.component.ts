import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService, AdminUser, ProfileUpdateData } from '../../services/auth.service';

interface ProfileData {
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  phoneNumber: string;
  profilePhoto?: string;
}

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css']
})
export class ProfileComponent implements OnInit {
  isDarkMode: boolean = false;
  isEditing: boolean = false;
  isLoading: boolean = false;
  isSaving: boolean = false;
  
  profileData: ProfileData = {
    firstName: '',
    lastName: '',
    email: '',
    role: '',
    phoneNumber: '',
    profilePhoto: ''
  };

  originalProfileData: ProfileData = {
    firstName: '',
    lastName: '',
    email: '',
    role: '',
    phoneNumber: '',
    profilePhoto: ''
  };

  selectedFile: File | null = null;
  previewUrl: string | null = null;

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadDarkModePreference();
    this.loadProfileData();
  }

  private loadDarkModePreference(): void {
    const savedDarkMode = localStorage.getItem('darkMode');
    this.isDarkMode = savedDarkMode === 'true';
  }

  private loadProfileData(): void {
    this.isLoading = true;

    // Get profile details from auth service
    this.authService.getProfileDetails().subscribe({
      next: (admin) => {
        if (admin) {
          // Parse the full name to get first and last name
          const nameParts = admin.fullName.split(' ');
          const firstName = nameParts[0] || '';
          const lastName = nameParts.slice(1).join(' ') || '';

          this.profileData = {
            firstName: firstName,
            lastName: lastName,
            email: admin.email,
            role: admin.role,
            phoneNumber: admin.phoneNumber || '',
            profilePhoto: admin.profilePhoto || this.getDefaultProfilePhoto(firstName)
          };

          // Store original data for reset functionality
          this.originalProfileData = { ...this.profileData };
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading profile data:', error);
        this.isLoading = false;
      }
    });
  }

  private getDefaultProfilePhoto(firstName: string): string {
    const initial = firstName.charAt(0).toUpperCase() || 'A';
    return `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 120 120'%3E%3Crect width='120' height='120' fill='%233B82F6'/%3E%3Ctext x='60' y='75' text-anchor='middle' fill='white' font-family='Arial' font-size='48' font-weight='bold'%3E${initial}%3C/text%3E%3C/svg%3E`;
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file.');
        return;
      }
      
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB.');
        return;
      }
      
      this.selectedFile = file;
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        this.previewUrl = e.target?.result as string;
      };
      reader.readAsDataURL(file);
    }
  }

  removePhoto(): void {
    this.selectedFile = null;
    this.previewUrl = null;
    this.profileData.profilePhoto = this.getDefaultProfilePhoto(this.profileData.firstName);
  }

  toggleEdit(): void {
    if (this.isEditing) {
      // Cancel editing - reset to original data
      this.profileData = { ...this.originalProfileData };
      this.selectedFile = null;
      this.previewUrl = null;
    }
    this.isEditing = !this.isEditing;
  }

  saveProfile(): void {
    if (!this.validateForm()) {
      return;
    }

    this.isSaving = true;

    // Prepare profile update data
    const updateData: ProfileUpdateData = {
      firstName: this.profileData.firstName,
      lastName: this.profileData.lastName,
      email: this.profileData.email,
      phoneNumber: this.profileData.phoneNumber,
      profilePhoto: this.previewUrl || this.profileData.profilePhoto
    };

    // Update profile using auth service
    this.authService.updateProfile(updateData).subscribe({
      next: (success) => {
        if (success) {
          // Update original data
          this.originalProfileData = { ...this.profileData };

          this.isSaving = false;
          this.isEditing = false;
          this.selectedFile = null;
          this.previewUrl = null;

          alert('Profile updated successfully!');
        } else {
          this.isSaving = false;
          alert('Failed to update profile. Please try again.');
        }
      },
      error: (error) => {
        console.error('Profile update error:', error);
        this.isSaving = false;
        alert('An error occurred while updating your profile.');
      }
    });
  }

  private validateForm(): boolean {
    if (!this.profileData.firstName.trim()) {
      alert('First name is required.');
      return false;
    }
    
    if (!this.profileData.lastName.trim()) {
      alert('Last name is required.');
      return false;
    }
    
    if (!this.profileData.email.trim()) {
      alert('Email is required.');
      return false;
    }
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(this.profileData.email)) {
      alert('Please enter a valid email address.');
      return false;
    }
    
    return true;
  }

  goBack(): void {
    this.router.navigate(['/dashboard']);
  }

  // Utility methods for styling
  getContainerClasses(): string {
    return this.isDarkMode 
      ? 'min-h-screen bg-gray-900 text-white' 
      : 'min-h-screen bg-gray-50 text-gray-900';
  }

  getCardClasses(): string {
    return this.isDarkMode 
      ? 'bg-gray-800 border-gray-700' 
      : 'bg-white border-gray-200';
  }

  getInputClasses(): string {
    return this.isDarkMode 
      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500' 
      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500';
  }

  getButtonClasses(type: 'primary' | 'secondary' | 'danger'): string {
    const baseClasses = 'px-4 py-2 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed';
    
    switch (type) {
      case 'primary':
        return `${baseClasses} bg-blue-600 hover:bg-blue-700 text-white`;
      case 'secondary':
        return this.isDarkMode 
          ? `${baseClasses} bg-gray-700 hover:bg-gray-600 text-white border border-gray-600` 
          : `${baseClasses} bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300`;
      case 'danger':
        return `${baseClasses} bg-red-600 hover:bg-red-700 text-white`;
      default:
        return baseClasses;
    }
  }
}
