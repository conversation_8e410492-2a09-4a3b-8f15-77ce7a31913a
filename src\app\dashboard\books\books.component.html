<div class="min-h-screen p-6" [class]="isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold mb-2">Books & Catalog</h1>
        <p class="text-gray-600 dark:text-gray-400">Manage your library's book collection</p>
      </div>
      <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add New Book
      </button>
    </div>
  </div>

  <!-- Stats Overview - Small Boxes in Row -->
  <div class="flex gap-4 mb-8">
    <!-- Box 1: Total Books -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-blue-100 dark:bg-blue-900">
          <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1 text-gray-600 dark:text-gray-400">Total Books</p>
        <p class="text-xl font-bold text-gray-900 dark:text-white">3,456</p>
      </div>
    </div>

    <!-- Box 2: Available -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-green-100 dark:bg-green-900">
          <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1 text-gray-600 dark:text-gray-400">Available</p>
        <p class="text-xl font-bold text-gray-900 dark:text-white">2,891</p>
      </div>
    </div>

    <!-- Box 3: Borrowed -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-orange-100 dark:bg-orange-900">
          <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1 text-gray-600 dark:text-gray-400">Borrowed</p>
        <p class="text-xl font-bold text-gray-900 dark:text-white">565</p>
      </div>
    </div>

    <!-- Box 4: Overdue -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-red-100 dark:bg-red-900">
          <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1 text-gray-600 dark:text-gray-400">Overdue</p>
        <p class="text-xl font-bold text-gray-900 dark:text-white">23</p>
      </div>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
      <div class="flex-1 max-w-md">
        <div class="relative">
          <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <input type="text" placeholder="Search books by title, author, or ISBN..." class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
        </div>
      </div>
      <div class="flex gap-3">
        <select class="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          <option>All Categories</option>
          <option>Fiction</option>
          <option>Non-Fiction</option>
          <option>Science</option>
          <option>History</option>
          <option>Technology</option>
        </select>
        <select class="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          <option>All Status</option>
          <option>Available</option>
          <option>Borrowed</option>
          <option>Reserved</option>
          <option>Maintenance</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Books Table -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold">Book Collection</h3>
    </div>
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Book Details</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Category</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Location</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
            <td class="px-6 py-4">
              <div class="flex items-center">
                <div class="w-12 h-16 bg-blue-100 dark:bg-blue-900 rounded flex items-center justify-center mr-4">
                  <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                  </svg>
                </div>
                <div>
                  <div class="text-sm font-medium">The Great Gatsby</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">by F. Scott Fitzgerald</div>
                  <div class="text-xs text-gray-400">ISBN: 978-0-7432-7356-5</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 text-sm">Fiction</td>
            <td class="px-6 py-4">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Available</span>
            </td>
            <td class="px-6 py-4 text-sm">A-1-001</td>
            <td class="px-6 py-4 text-sm">
              <div class="flex space-x-2">
                <button class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">Edit</button>
                <button class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">View</button>
                <button class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">Delete</button>
              </div>
            </td>
          </tr>
          <!-- More sample rows would go here -->
        </tbody>
      </table>
    </div>
  </div>
</div>
