import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Student {
  id: string;
  name: string;
}

interface Book {
  title: string;
  author: string;
}

interface Loan {
  id: string;
  student: Student;
  book: Book;
  loanDate: string;
  dueDate: string;
  status: 'Active' | 'Overdue' | 'Due Today';
}

interface BorrowingStats {
  activeLoans: number;
  returnsToday: number;
  overdueItems: number;
  dueToday: number;
}

@Component({
  selector: 'app-borrowing',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './borrowing.component.html',
  styleUrls: ['./borrowing.component.css']
})
export class BorrowingComponent implements OnInit {
  isDarkMode: boolean = false;

  stats: BorrowingStats = {
    activeLoans: 234,
    returnsToday: 18,
    overdueItems: 12,
    dueToday: 8
  };

  loans: Loan[] = [
    {
      id: 'L001',
      student: { id: 'S2024001', name: '<PERSON>' },
      book: { title: 'Introduction to Computer Science', author: '<PERSON>' },
      loanDate: '2024-07-15',
      dueDate: '2024-07-29',
      status: 'Active'
    },
    {
      id: 'L002',
      student: { id: 'S2024002', name: '<PERSON>a <PERSON>' },
      book: { title: 'Advanced Mathematics', author: 'Dr. <PERSON>' },
      loanDate: '2024-07-10',
      dueDate: '2024-07-24',
      status: 'Overdue'
    },
    {
      id: 'L003',
      student: { id: 'S2024003', name: 'Ana Rodriguez' },
      book: { title: 'Philippine History', author: 'Prof. Garcia' },
      loanDate: '2024-07-20',
      dueDate: '2024-07-27',
      status: 'Due Today'
    },
    {
      id: 'L004',
      student: { id: 'S2024004', name: 'Carlos Mendoza' },
      book: { title: 'English Literature', author: 'Shakespeare' },
      loanDate: '2024-07-18',
      dueDate: '2024-08-01',
      status: 'Active'
    },
    {
      id: 'L005',
      student: { id: 'S2024005', name: 'Lisa Chen' },
      book: { title: 'Biology Fundamentals', author: 'Dr. Wilson' },
      loanDate: '2024-07-12',
      dueDate: '2024-07-26',
      status: 'Overdue'
    }
  ];

  constructor() { }

  ngOnInit(): void {
    this.isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
  }

  getTextClasses(): string {
    return this.isDarkMode ? 'text-white' : 'text-gray-900';
  }

  getSecondaryTextClasses(): string {
    return this.isDarkMode ? 'text-gray-400' : 'text-gray-600';
  }

  getCardClasses(): string {
    return this.isDarkMode 
      ? 'bg-gray-800 border-gray-700 text-white' 
      : 'bg-white border-gray-200 text-gray-900';
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800';
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      case 'Due Today':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
}
