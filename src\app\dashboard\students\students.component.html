<div class="min-h-screen p-6" [class]="isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold mb-2">Student Management</h1>
        <p class="text-gray-600 dark:text-gray-400">Manage student accounts and library access</p>
      </div>
      <button class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add New Student
      </button>
    </div>
  </div>

  <!-- Stats Overview - Small Boxes in Row -->
  <div class="flex gap-4 mb-8">
    <!-- Box 1: Total Students -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-green-100 dark:bg-green-900">
          <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1 text-gray-600 dark:text-gray-400">Total Students</p>
        <p class="text-xl font-bold text-gray-900 dark:text-white">1,234</p>
      </div>
    </div>

    <!-- Box 2: Active Members -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-blue-100 dark:bg-blue-900">
          <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1 text-gray-600 dark:text-gray-400">Active Members</p>
        <p class="text-xl font-bold text-gray-900 dark:text-white">1,156</p>
      </div>
    </div>

    <!-- Box 3: With Borrowed Books -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-orange-100 dark:bg-orange-900">
          <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1 text-gray-600 dark:text-gray-400">With Borrowed Books</p>
        <p class="text-xl font-bold text-gray-900 dark:text-white">342</p>
      </div>
    </div>

    <!-- Box 4: Overdue Books -->
    <div class="stat-box flex-1 p-4 rounded-lg border transition-colors duration-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <div class="text-center">
        <div class="w-8 h-8 mx-auto mb-2 p-1 rounded-full bg-red-100 dark:bg-red-900">
          <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
        <p class="text-xs font-medium mb-1 text-gray-600 dark:text-gray-400">Overdue Books</p>
        <p class="text-xl font-bold text-gray-900 dark:text-white">18</p>
      </div>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
      <div class="flex-1 max-w-md">
        <div class="relative">
          <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <input type="text" placeholder="Search students by name, ID, or email..." class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
        </div>
      </div>
      <div class="flex gap-3">
        <select class="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          <option>All Programs</option>
          <option>Computer Science</option>
          <option>Engineering</option>
          <option>Business</option>
          <option>Arts & Sciences</option>
        </select>
        <select class="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          <option>All Years</option>
          <option>1st Year</option>
          <option>2nd Year</option>
          <option>3rd Year</option>
          <option>4th Year</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Students Table -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold">Student Directory</h3>
    </div>
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Student</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Student ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Program</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Year Level</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
            <td class="px-6 py-4">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mr-4">
                  <span class="text-green-600 dark:text-green-400 font-medium">JD</span>
                </div>
                <div>
                  <div class="text-sm font-medium">John Doe</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">john.doe&#64;benedicto.edu</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 text-sm">2021-00123</td>
            <td class="px-6 py-4 text-sm">Computer Science</td>
            <td class="px-6 py-4 text-sm">3rd Year</td>
            <td class="px-6 py-4">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Active</span>
            </td>
            <td class="px-6 py-4 text-sm">
              <div class="flex space-x-2">
                <button class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">Edit</button>
                <button class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">View</button>
                <button class="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300">Suspend</button>
              </div>
            </td>
          </tr>
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
            <td class="px-6 py-4">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-4">
                  <span class="text-blue-600 dark:text-blue-400 font-medium">JS</span>
                </div>
                <div>
                  <div class="text-sm font-medium">Jane Smith</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">jane.smith&#64;benedicto.edu</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 text-sm">2020-00456</td>
            <td class="px-6 py-4 text-sm">Engineering</td>
            <td class="px-6 py-4 text-sm">4th Year</td>
            <td class="px-6 py-4">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Active</span>
            </td>
            <td class="px-6 py-4 text-sm">
              <div class="flex space-x-2">
                <button class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">Edit</button>
                <button class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">View</button>
                <button class="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300">Suspend</button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
