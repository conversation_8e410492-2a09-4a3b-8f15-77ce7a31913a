<div [class]="getContainerClasses()">
  <!-- Hero Section with Background -->
  <div class="relative">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 opacity-10"></div>
    <div class="absolute inset-0" [class]="isDarkMode ? 'bg-gray-900/50' : 'bg-white/50'"></div>

    <!-- Header Content -->
    <div class="relative z-10 container mx-auto px-4 py-8 max-w-6xl">
      <!-- Navigation Header -->
      <div class="flex items-center justify-between mb-8">
        <div class="flex items-center space-x-4">
          <button
            (click)="goBack()"
            class="group flex items-center space-x-3 px-4 py-2 rounded-xl transition-all duration-300 hover:scale-105"
            [class]="isDarkMode ? 'bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white border border-gray-700' : 'bg-white hover:bg-gray-50 text-gray-700 hover:text-gray-900 border border-gray-200 shadow-sm hover:shadow-md'"
          >
            <svg class="w-5 h-5 transition-transform duration-300 group-hover:-translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            <span class="font-medium">Back to Dashboard</span>
          </button>
        </div>

        <!-- Page Title with Icon -->
        <div class="flex items-center space-x-3">
          <div class="p-3 rounded-xl" [class]="isDarkMode ? 'bg-blue-500/20 text-blue-400' : 'bg-blue-100 text-blue-600'">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
          <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            User Profile
          </h1>
        </div>
      </div>
      
      <!-- Edit/Save Controls -->
      <div class="flex justify-end space-x-4">
        <button
          *ngIf="!isEditing"
          (click)="toggleEdit()"
          class="group flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg"
        >
          <svg class="w-5 h-5 transition-transform duration-300 group-hover:rotate-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          <span>Edit Profile</span>
        </button>

        <div *ngIf="isEditing" class="flex space-x-3">
          <button
            (click)="toggleEdit()"
            class="group flex items-center space-x-3 px-6 py-3 font-medium rounded-xl transition-all duration-300 hover:scale-105"
            [class]="isDarkMode ? 'bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white border border-gray-600' : 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:text-gray-900 border border-gray-300'"
            [disabled]="isSaving"
          >
            <svg class="w-5 h-5 transition-transform duration-300 group-hover:rotate-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            <span>Cancel</span>
          </button>

          <button
            (click)="saveProfile()"
            class="group flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
            [disabled]="isSaving"
          >
            <svg *ngIf="!isSaving" class="w-5 h-5 transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <svg *ngIf="isSaving" class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>{{ isSaving ? 'Saving...' : 'Save Changes' }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex items-center justify-center py-12">
      <div class="flex items-center space-x-3">
        <svg class="w-8 h-8 animate-spin text-blue-600" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-lg">Loading profile...</span>
      </div>
    </div>

    <!-- Profile Content -->
    <div *ngIf="!isLoading" class="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
      <!-- Profile Photo Section -->
      <div class="lg:col-span-1">
        <div class="relative overflow-hidden rounded-2xl shadow-xl border backdrop-blur-sm"
             [class]="isDarkMode ? 'bg-gray-800/80 border-gray-700' : 'bg-white/80 border-gray-200'">
          <!-- Card Header -->
          <div class="relative p-6 pb-4">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10"></div>
            <div class="relative flex items-center space-x-3">
              <div class="p-2 rounded-lg" [class]="isDarkMode ? 'bg-blue-500/20 text-blue-400' : 'bg-blue-100 text-blue-600'">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </div>
              <h2 class="text-xl font-bold">Profile Photo</h2>
            </div>
          </div>

          <div class="px-6 pb-6">
            <div class="flex flex-col items-center space-y-6">
              <!-- Current Photo -->
              <div class="relative group">
                <div class="relative">
                  <img
                    [src]="previewUrl || profileData.profilePhoto"
                    [alt]="profileData.firstName + ' ' + profileData.lastName"
                    class="w-40 h-40 rounded-full object-cover shadow-2xl ring-4 ring-white/20 transition-all duration-300 group-hover:scale-105"
                  >
                  <!-- Photo overlay for editing -->
                  <div *ngIf="isEditing" class="absolute inset-0 rounded-full bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                  </div>
                </div>

                <!-- Remove Photo Button -->
                <div *ngIf="isEditing && (previewUrl || selectedFile)"
                     class="absolute -top-2 -right-2">
                  <button
                    (click)="removePhoto()"
                    class="group bg-red-500 hover:bg-red-600 text-white rounded-full p-2 shadow-lg transition-all duration-300 hover:scale-110"
                  >
                    <svg class="w-4 h-4 transition-transform duration-300 group-hover:rotate-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
              </div>
            
              <!-- Upload Controls -->
              <div *ngIf="isEditing" class="w-full space-y-4">
                <label class="block">
                  <input
                    type="file"
                    accept="image/*"
                    (change)="onFileSelected($event)"
                    class="hidden"
                  >
                  <div class="group w-full text-center cursor-pointer flex items-center justify-center space-x-3 px-6 py-4 rounded-xl border-2 border-dashed transition-all duration-300 hover:scale-105"
                       [class]="isDarkMode ? 'border-gray-600 hover:border-blue-500 bg-gray-700/50 hover:bg-gray-700 text-gray-300 hover:text-white' : 'border-gray-300 hover:border-blue-500 bg-gray-50 hover:bg-blue-50 text-gray-700 hover:text-blue-700'">
                    <svg class="w-6 h-6 transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <span class="font-medium">Upload New Photo</span>
                  </div>
                </label>
                <div class="text-center">
                  <p class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">
                    <span class="font-medium">Max file size:</span> 5MB
                  </p>
                  <p class="text-xs mt-1" [class]="isDarkMode ? 'text-gray-500' : 'text-gray-400'">
                    Supported formats: JPG, PNG, GIF
                  </p>
                </div>
              </div>

              <!-- User Info Display (when not editing) -->
              <div *ngIf="!isEditing" class="text-center space-y-2">
                <h3 class="text-xl font-bold">{{ profileData.firstName }} {{ profileData.lastName }}</h3>
                <p class="text-sm px-3 py-1 rounded-full inline-block"
                   [class]="isDarkMode ? 'bg-blue-500/20 text-blue-400' : 'bg-blue-100 text-blue-700'">
                  {{ profileData.role }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Profile Information Section -->
      <div class="lg:col-span-2">
        <div class="relative overflow-hidden rounded-2xl shadow-xl border backdrop-blur-sm"
             [class]="isDarkMode ? 'bg-gray-800/80 border-gray-700' : 'bg-white/80 border-gray-200'">
          <!-- Card Header -->
          <div class="relative p-6 pb-4">
            <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10"></div>
            <div class="relative flex items-center space-x-3">
              <div class="p-2 rounded-lg" [class]="isDarkMode ? 'bg-purple-500/20 text-purple-400' : 'bg-purple-100 text-purple-600'">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <h2 class="text-xl font-bold">Personal Information</h2>
            </div>
          </div>

          <div class="px-6 pb-6">
            <form class="space-y-6">
              <!-- Name Fields -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                  <label class="flex items-center space-x-2 text-sm font-medium">
                    <svg class="w-4 h-4" [class]="isDarkMode ? 'text-blue-400' : 'text-blue-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span>First Name</span>
                  </label>
                  <input
                    type="text"
                    [(ngModel)]="profileData.firstName"
                    name="firstName"
                    [readonly]="!isEditing"
                    class="w-full px-4 py-3 rounded-xl border transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                    [class]="isEditing ?
                      (isDarkMode ? 'bg-gray-700 border-gray-600 text-white focus:bg-gray-600' : 'bg-white border-gray-300 text-gray-900 focus:bg-gray-50') :
                      (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
                    placeholder="Enter first name"
                  >
                </div>

                <div class="space-y-2">
                  <label class="flex items-center space-x-2 text-sm font-medium">
                    <svg class="w-4 h-4" [class]="isDarkMode ? 'text-blue-400' : 'text-blue-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span>Last Name</span>
                  </label>
                  <input
                    type="text"
                    [(ngModel)]="profileData.lastName"
                    name="lastName"
                    [readonly]="!isEditing"
                    class="w-full px-4 py-3 rounded-xl border transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                    [class]="isEditing ?
                      (isDarkMode ? 'bg-gray-700 border-gray-600 text-white focus:bg-gray-600' : 'bg-white border-gray-300 text-gray-900 focus:bg-gray-50') :
                      (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
                    placeholder="Enter last name"
                  >
                </div>
              </div>

              <!-- Email Field -->
              <div class="space-y-2">
                <label class="flex items-center space-x-2 text-sm font-medium">
                  <svg class="w-4 h-4" [class]="isDarkMode ? 'text-green-400' : 'text-green-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                  <span>Email Address</span>
                </label>
                <input
                  type="email"
                  [(ngModel)]="profileData.email"
                  name="email"
                  [readonly]="!isEditing"
                  class="w-full px-4 py-3 rounded-xl border transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  [class]="isEditing ?
                    (isDarkMode ? 'bg-gray-700 border-gray-600 text-white focus:bg-gray-600' : 'bg-white border-gray-300 text-gray-900 focus:bg-gray-50') :
                    (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
                  placeholder="Enter email address"
                >
              </div>

              <!-- Role Field -->
              <div class="space-y-2">
                <label class="flex items-center space-x-2 text-sm font-medium">
                  <svg class="w-4 h-4" [class]="isDarkMode ? 'text-purple-400' : 'text-purple-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                  </svg>
                  <span>Role</span>
                </label>
                <div class="relative">
                  <input
                    type="text"
                    [(ngModel)]="profileData.role"
                    name="role"
                    readonly
                    class="w-full px-4 py-3 rounded-xl border transition-all duration-300 cursor-not-allowed"
                    [class]="isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-400' : 'bg-gray-100 border-gray-300 text-gray-600'"
                    placeholder="Role"
                  >
                  <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <svg class="w-4 h-4" [class]="isDarkMode ? 'text-gray-600' : 'text-gray-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                  </div>
                </div>
                <p class="text-xs flex items-center space-x-1" [class]="isDarkMode ? 'text-gray-500' : 'text-gray-400'">
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span>Role cannot be changed</span>
                </p>
              </div>

              <!-- Phone Number Field -->
              <div class="space-y-2">
                <label class="flex items-center space-x-2 text-sm font-medium">
                  <svg class="w-4 h-4" [class]="isDarkMode ? 'text-orange-400' : 'text-orange-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                  </svg>
                  <span>Phone Number</span>
                </label>
                <input
                  type="tel"
                [(ngModel)]="profileData.phoneNumber"
                name="phoneNumber"
                [readonly]="!isEditing"
                [class]="getInputClasses()"
                class="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:ring-blue-500 focus:outline-none transition-colors duration-200"
                placeholder="Enter phone number"
              >
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
