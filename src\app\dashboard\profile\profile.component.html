<div [class]="getContainerClasses()">
  <div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
      <div class="flex items-center space-x-4">
        <button 
          (click)="goBack()"
          [class]="getButtonClasses('secondary')"
          class="flex items-center space-x-2"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          <span>Back to Dashboard</span>
        </button>
        <h1 class="text-3xl font-bold">User Profile</h1>
      </div>
      
      <div class="flex items-center space-x-3">
        <button 
          *ngIf="!isEditing"
          (click)="toggleEdit()"
          [class]="getButtonClasses('primary')"
          class="flex items-center space-x-2"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          <span>Edit Profile</span>
        </button>
        
        <div *ngIf="isEditing" class="flex items-center space-x-3">
          <button 
            (click)="saveProfile()"
            [disabled]="isSaving"
            [class]="getButtonClasses('primary')"
            class="flex items-center space-x-2"
          >
            <svg *ngIf="!isSaving" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <svg *ngIf="isSaving" class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>{{ isSaving ? 'Saving...' : 'Save Changes' }}</span>
          </button>
          
          <button 
            (click)="toggleEdit()"
            [disabled]="isSaving"
            [class]="getButtonClasses('secondary')"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex items-center justify-center py-12">
      <div class="flex items-center space-x-3">
        <svg class="w-8 h-8 animate-spin text-blue-600" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-lg">Loading profile...</span>
      </div>
    </div>

    <!-- Profile Content -->
    <div *ngIf="!isLoading" class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Profile Photo Section -->
      <div class="lg:col-span-1">
        <div [class]="getCardClasses()" class="rounded-xl shadow-lg border p-6">
          <h2 class="text-xl font-semibold mb-6">Profile Photo</h2>
          
          <div class="flex flex-col items-center space-y-4">
            <!-- Current Photo -->
            <div class="relative">
              <img
                [src]="previewUrl || profileData.profilePhoto"
                [alt]="profileData.firstName + ' ' + profileData.lastName"
                class="w-32 h-32 rounded-full object-cover shadow-lg"
              >
              <div *ngIf="isEditing && (previewUrl || selectedFile)" 
                   class="absolute -top-2 -right-2">
                <button 
                  (click)="removePhoto()"
                  class="bg-red-500 hover:bg-red-600 text-white rounded-full p-1 shadow-lg transition-colors duration-200"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>
            
            <!-- Upload Controls -->
            <div *ngIf="isEditing" class="w-full">
              <label class="block">
                <input 
                  type="file" 
                  accept="image/*" 
                  (change)="onFileSelected($event)"
                  class="hidden"
                >
                <div [class]="getButtonClasses('secondary')" 
                     class="w-full text-center cursor-pointer flex items-center justify-center space-x-2">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                  <span>Upload New Photo</span>
                </div>
              </label>
              <p class="text-sm text-gray-500 mt-2 text-center">
                Max file size: 5MB<br>
                Supported formats: JPG, PNG, GIF
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Profile Information Section -->
      <div class="lg:col-span-2">
        <div [class]="getCardClasses()" class="rounded-xl shadow-lg border p-6">
          <h2 class="text-xl font-semibold mb-6">Personal Information</h2>
          
          <form class="space-y-6">
            <!-- Name Fields -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium mb-2">First Name</label>
                <input 
                  type="text" 
                  [(ngModel)]="profileData.firstName"
                  name="firstName"
                  [readonly]="!isEditing"
                  [class]="getInputClasses()"
                  class="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:ring-blue-500 focus:outline-none transition-colors duration-200"
                  placeholder="Enter first name"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium mb-2">Last Name</label>
                <input 
                  type="text" 
                  [(ngModel)]="profileData.lastName"
                  name="lastName"
                  [readonly]="!isEditing"
                  [class]="getInputClasses()"
                  class="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:ring-blue-500 focus:outline-none transition-colors duration-200"
                  placeholder="Enter last name"
                >
              </div>
            </div>

            <!-- Email Field -->
            <div>
              <label class="block text-sm font-medium mb-2">Email Address</label>
              <input 
                type="email" 
                [(ngModel)]="profileData.email"
                name="email"
                [readonly]="!isEditing"
                [class]="getInputClasses()"
                class="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:ring-blue-500 focus:outline-none transition-colors duration-200"
                placeholder="Enter email address"
              >
            </div>

            <!-- Role Field -->
            <div>
              <label class="block text-sm font-medium mb-2">Role</label>
              <input 
                type="text" 
                [(ngModel)]="profileData.role"
                name="role"
                readonly
                [class]="getInputClasses()"
                class="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:ring-blue-500 focus:outline-none transition-colors duration-200 opacity-60 cursor-not-allowed"
                placeholder="Role"
              >
              <p class="text-sm text-gray-500 mt-1">Role cannot be changed</p>
            </div>

            <!-- Phone Number Field -->
            <div>
              <label class="block text-sm font-medium mb-2">Phone Number</label>
              <input 
                type="tel" 
                [(ngModel)]="profileData.phoneNumber"
                name="phoneNumber"
                [readonly]="!isEditing"
                [class]="getInputClasses()"
                class="w-full px-4 py-3 rounded-lg border focus:ring-2 focus:ring-blue-500 focus:outline-none transition-colors duration-200"
                placeholder="Enter phone number"
              >
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
