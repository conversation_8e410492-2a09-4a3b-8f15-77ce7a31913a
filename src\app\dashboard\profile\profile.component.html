<div [class]="getContainerClasses()">
  <div class="min-h-screen flex items-center justify-center p-4">
    <!-- Main Profile Card -->
    <div class="w-full max-w-2xl rounded-3xl shadow-2xl border"
         [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">

      <!-- Header Section -->
      <div class="p-8 pb-6">
        <div class="flex items-center justify-between mb-8">
          <!-- User Icon and Title -->
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 rounded-full flex items-center justify-center"
                 [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-900'">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
              </svg>
            </div>
            <span class="text-lg font-semibold">User</span>
          </div>

          <!-- Action Buttons -->
          <div class="flex items-center space-x-3">
            <button
              (click)="goBack()"
              class="px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200"
              [class]="isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
            >
              Back to Dashboard
            </button>
            <button
              *ngIf="!isEditing"
              class="px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200"
              [class]="isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
            >
              Account Settings
            </button>
            <button
              *ngIf="!isEditing"
              (click)="toggleEdit()"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors duration-200"
            >
              Edit Profile
            </button>

            <!-- Edit Mode Buttons -->
            <div *ngIf="isEditing" class="flex items-center space-x-3">
              <button
                (click)="toggleEdit()"
                class="px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200"
                [class]="isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
                [disabled]="isSaving"
              >
                Cancel
              </button>
              <button
                (click)="saveProfile()"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors duration-200 disabled:opacity-50"
                [disabled]="isSaving"
              >
                {{ isSaving ? 'Saving...' : 'Save Changes' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Profile Header -->
        <div class="flex items-center space-x-6 mb-8">
          <!-- Profile Photo -->
          <div class="relative">
            <img
              [src]="previewUrl || profileData.profilePhoto"
              [alt]="profileData.firstName + ' ' + profileData.lastName"
              class="w-24 h-24 rounded-full object-cover"
            >
            <!-- Edit Photo Button -->
            <label
              *ngIf="isEditing"
              for="fileInput"
              class="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-600 hover:bg-blue-700 text-white rounded-full flex items-center justify-center transition-colors duration-200 cursor-pointer"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </label>
            <!-- Hidden File Input -->
            <input
              id="fileInput"
              type="file"
              accept="image/*"
              (change)="onFileSelected($event)"
              class="hidden"
            >
          </div>

          <!-- Profile Info -->
          <div>
            <h1 class="text-2xl font-bold mb-1">Profile</h1>
            <p class="text-lg" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-600'">
              {{ profileData.firstName }} {{ profileData.lastName }}
            </p>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading" class="flex items-center justify-center py-12">
        <div class="flex items-center space-x-3">
          <svg class="w-6 h-6 animate-spin text-blue-600" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-sm">Loading profile...</span>
        </div>
      </div>

      <!-- Profile Content -->
      <div *ngIf="!isLoading" class="p-8 pt-0">
        <!-- Profile Form -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Left Column -->
          <div class="space-y-6">
            <!-- Name Field -->
            <div>
              <div class="flex items-center justify-between mb-2">
                <label class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Name</label>
                <button
                  *ngIf="!isEditing"
                  (click)="toggleEdit()"
                  class="text-blue-600 hover:text-blue-700"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                </button>
              </div>
              <input
                type="text"
                [value]="profileData.firstName + ' ' + profileData.lastName"
                [readonly]="!isEditing"
                class="w-full px-4 py-3 rounded-lg border transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                [class]="isEditing ?
                  (isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900') :
                  (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
              >
            </div>

            <!-- Email Field -->
            <div>
              <label class="block text-sm font-medium mb-2" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Email</label>
              <input
                type="email"
                [(ngModel)]="profileData.email"
                name="email"
                [readonly]="!isEditing"
                class="w-full px-4 py-3 rounded-lg border transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                [class]="isEditing ?
                  (isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900') :
                  (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
                placeholder="Enter email address"
              >
            </div>

            <!-- Phone Field -->
            <div>
              <label class="block text-sm font-medium mb-2" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Phone</label>
              <input
                type="tel"
                [(ngModel)]="profileData.phoneNumber"
                name="phoneNumber"
                [readonly]="!isEditing"
                class="w-full px-4 py-3 rounded-lg border transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                [class]="isEditing ?
                  (isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900') :
                  (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
                placeholder="Enter phone number"
              >
            </div>

            <!-- Role Field -->
            <div>
              <div class="flex items-center justify-between mb-2">
                <label class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Role</label>
                <svg class="w-4 h-4" [class]="isDarkMode ? 'text-gray-500' : 'text-gray-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <input
                type="text"
                [(ngModel)]="profileData.role"
                name="role"
                readonly
                class="w-full px-4 py-3 rounded-lg border transition-colors duration-200 cursor-not-allowed"
                [class]="isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-400' : 'bg-gray-100 border-gray-300 text-gray-600'"
                placeholder="Role"
              >
            </div>
          </div>

          <!-- Right Column -->
          <div class="space-y-6">
            <!-- Vocation Section -->
            <div>
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Vocation</h3>
                <button
                  *ngIf="!isEditing"
                  class="text-blue-600 hover:text-blue-700"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                </button>
              </div>
              <div class="flex items-center justify-between p-4 rounded-lg border"
                   [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'">
                <span class="text-sm" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">{{ profileData.role || 'Not specified' }}</span>
                <svg class="w-4 h-4" [class]="isDarkMode ? 'text-gray-500' : 'text-gray-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </div>

            <!-- Interests Section -->
            <div>
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Interests</h3>
                <button
                  *ngIf="!isEditing"
                  class="text-blue-600 hover:text-blue-700"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                </button>
              </div>
              <div class="space-y-3">
                <div class="flex items-center justify-between p-4 rounded-lg border"
                     [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'">
                  <span class="text-sm" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Reading</span>
                  <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4" [class]="isDarkMode ? 'text-gray-500' : 'text-gray-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <svg class="w-4 h-4" [class]="isDarkMode ? 'text-gray-500' : 'text-gray-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"></path>
                    </svg>
                  </div>
                </div>
                <div class="flex items-center justify-between p-4 rounded-lg border"
                     [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'">
                  <div>
                    <div class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Technology</div>
                    <div class="text-xs" [class]="isDarkMode ? 'text-gray-500' : 'text-gray-500'">Learning about new innovations</div>
                  </div>
                  <button class="text-blue-600 hover:text-blue-700 text-sm">Edit</button>
                </div>
              </div>
            </div>
          </div>
        </div>


        <!-- Edit Button -->
        <div *ngIf="!isEditing" class="mt-8 flex justify-center">
          <button
            (click)="toggleEdit()"
            class="px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200"
          >
            Edit
          </button>
        </div>

        <!-- Save/Cancel Buttons -->
        <div *ngIf="isEditing" class="mt-8 flex justify-center space-x-4">
          <button
            (click)="toggleEdit()"
            class="px-6 py-3 border rounded-lg font-medium transition-colors duration-200"
            [class]="isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
            [disabled]="isSaving"
          >
            Cancel
          </button>
          <button
            (click)="saveProfile()"
            class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 disabled:opacity-50"
            [disabled]="isSaving"
          >
            {{ isSaving ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
