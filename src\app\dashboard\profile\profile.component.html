<div class="min-h-screen" [class]="isDarkMode ? 'bg-gray-900' : 'bg-gray-100'">
  <div class="flex min-h-screen">

    <!-- Side Profile Panel - Fixed Width -->
    <div class="w-80 flex-none border-r overflow-y-auto" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">
    <div class="p-8">
      <!-- Profile Photo Section -->
      <div class="text-center mb-8">
        <div class="relative inline-block">
          <img
            [src]="previewUrl || profileData.profilePhoto"
            [alt]="profileData.firstName + ' ' + profileData.lastName"
            class="w-32 h-32 rounded-full object-cover shadow-lg mx-auto"
          >
          <!-- Edit Photo Button -->
          <label
            *ngIf="isEditing"
            for="fileInput"
            class="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-600 hover:bg-blue-700 text-white rounded-full flex items-center justify-center transition-colors duration-200 cursor-pointer"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </label>
          <!-- Hidden File Input -->
          <input
            id="fileInput"
            type="file"
            accept="image/*"
            (change)="onFileSelected($event)"
            class="hidden"
          >
        </div>
      </div>

      <!-- Personal Information -->
      <div class="space-y-6">
        <div>
          <h3 class="text-lg font-semibold mb-4 flex items-center space-x-2" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span>Personal Information</span>
          </h3>

          <!-- Name Details -->
          <div class="space-y-4">
            <div>
              <label class="text-xs font-medium uppercase tracking-wide flex items-center space-x-2 mb-2" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
                <span>First Name</span>
              </label>
              <div class="p-3 rounded-lg" [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-50'">
                <p class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-200' : 'text-gray-800'">
                  {{ profileData.firstName || 'N/A' }}
                </p>
              </div>
            </div>

            <div>
              <label class="text-xs font-medium uppercase tracking-wide flex items-center space-x-2 mb-2" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
                <span>Last Name</span>
              </label>
              <div class="p-3 rounded-lg" [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-50'">
                <p class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-200' : 'text-gray-800'">
                  {{ profileData.lastName || 'N/A' }}
                </p>
              </div>
            </div>

            <div>
              <label class="text-xs font-medium uppercase tracking-wide flex items-center space-x-2 mb-2" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span>Middle Initial</span>
              </label>
              <div class="p-3 rounded-lg" [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-50'">
                <p class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-200' : 'text-gray-800'">
                  N/A
                </p>
              </div>
            </div>

            <div>
              <label class="text-xs font-medium uppercase tracking-wide flex items-center space-x-2 mb-2" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span>Suffix</span>
              </label>
              <div class="p-3 rounded-lg" [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-50'">
                <p class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-200' : 'text-gray-800'">
                  N/A
                </p>
              </div>
            </div>

            <div>
              <label class="text-xs font-medium uppercase tracking-wide flex items-center space-x-2 mb-2" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                </svg>
                <span>Role</span>
              </label>
              <div class="p-3 rounded-lg" [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-50'">
                <p class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-200' : 'text-gray-800'">
                  {{ profileData.role || 'N/A' }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Information -->
        <div>
          <h3 class="text-lg font-semibold mb-4" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Contact Information</h3>

          <div class="space-y-4">
            <div>
              <label class="text-xs font-medium uppercase tracking-wide flex items-center space-x-2 mb-2" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <span>Email</span>
              </label>
              <div class="flex items-center space-x-3 p-3 rounded-lg" [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-50'">
                <svg class="w-5 h-5 flex-shrink-0" [class]="isDarkMode ? 'text-blue-400' : 'text-blue-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                </svg>
                <p class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-200' : 'text-gray-800'">
                  {{ profileData.email || 'N/A' }}
                </p>
              </div>
            </div>

            <div>
              <label class="text-xs font-medium uppercase tracking-wide flex items-center space-x-2 mb-2" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>Phone Number</span>
              </label>
              <div class="flex items-center space-x-3 p-3 rounded-lg" [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-50'">
                <svg class="w-5 h-5 flex-shrink-0" [class]="isDarkMode ? 'text-green-400' : 'text-green-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                </svg>
                <p class="text-sm font-medium" [class]="isDarkMode ? 'text-gray-200' : 'text-gray-800'">
                  {{ profileData.phoneNumber || 'N/A' }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content Area -->
  <div class="flex-1 p-8">
    <!-- Header Section -->
    <div class="flex items-center justify-between mb-8">
      <!-- Title -->
      <div>
        <h1 class="text-3xl font-bold" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Profile Settings</h1>
        <p class="text-lg" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-600'">
          Manage your account information and preferences
        </p>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center space-x-3">
        <button
          (click)="goBack()"
          class="px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200"
          [class]="isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
        >
          Back to Dashboard
        </button>
        <button
          *ngIf="!isEditing"
          (click)="toggleEdit()"
          class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors duration-200"
        >
          Edit Profile
        </button>

        <!-- Edit Mode Buttons -->
        <div *ngIf="isEditing" class="flex items-center space-x-3">
          <button
            (click)="toggleEdit()"
            class="px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200"
            [class]="isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
            [disabled]="isSaving"
          >
            Cancel
          </button>
          <button
            (click)="saveProfile()"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors duration-200 disabled:opacity-50"
            [disabled]="isSaving"
          >
            {{ isSaving ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex items-center justify-center py-12">
      <div class="flex items-center space-x-3">
        <svg class="w-6 h-6 animate-spin text-blue-600" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-sm">Loading profile...</span>
      </div>
    </div>

    <!-- Main Content -->
    <div *ngIf="!isLoading" class="space-y-8">

      <!-- Account Information Card -->
      <div class="rounded-xl shadow-lg" [class]="isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'">
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-6" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Account Information</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- First Name -->
            <div>
              <label class="block text-sm font-medium mb-2" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">First Name</label>
              <input
                type="text"
                [(ngModel)]="profileData.firstName"
                name="firstName"
                [readonly]="!isEditing"
                class="w-full px-4 py-3 rounded-lg border transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                [class]="isEditing ?
                  (isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900') :
                  (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
                placeholder="Enter first name"
              >
            </div>

            <!-- Last Name -->
            <div>
              <label class="block text-sm font-medium mb-2" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Last Name</label>
              <input
                type="text"
                [(ngModel)]="profileData.lastName"
                name="lastName"
                [readonly]="!isEditing"
                class="w-full px-4 py-3 rounded-lg border transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                [class]="isEditing ?
                  (isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900') :
                  (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
                placeholder="Enter last name"
              >
            </div>

            <!-- Email -->
            <div>
              <label class="block text-sm font-medium mb-2" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Email Address</label>
              <input
                type="email"
                [(ngModel)]="profileData.email"
                name="email"
                [readonly]="!isEditing"
                class="w-full px-4 py-3 rounded-lg border transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                [class]="isEditing ?
                  (isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900') :
                  (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
                placeholder="Enter email address"
              >
            </div>

            <!-- Phone Number -->
            <div>
              <label class="block text-sm font-medium mb-2" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Phone Number</label>
              <input
                type="tel"
                [(ngModel)]="profileData.phoneNumber"
                name="phoneNumber"
                [readonly]="!isEditing"
                class="w-full px-4 py-3 rounded-lg border transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                [class]="isEditing ?
                  (isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900') :
                  (isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-300' : 'bg-gray-50 border-gray-200 text-gray-700')"
                placeholder="Enter phone number"
              >
            </div>

            <!-- Role (Read-only) -->
            <div class="md:col-span-2">
              <label class="block text-sm font-medium mb-2" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Role</label>
              <input
                type="text"
                [(ngModel)]="profileData.role"
                name="role"
                readonly
                class="w-full px-4 py-3 rounded-lg border cursor-not-allowed"
                [class]="isDarkMode ? 'bg-gray-800 border-gray-700 text-gray-400' : 'bg-gray-100 border-gray-300 text-gray-600'"
                placeholder="Role"
              >
              <p class="text-xs mt-1" [class]="isDarkMode ? 'text-gray-500' : 'text-gray-400'">
                Role cannot be changed. Contact administrator for role changes.
              </p>
            </div>
          </div>
        </div>
      </div>


      <!-- Security & Preferences Card -->
      <div class="rounded-xl shadow-lg" [class]="isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'">
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-6" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Security & Preferences</h2>

          <div class="space-y-6">
            <!-- Password Change -->
            <div>
              <h3 class="text-sm font-medium mb-3" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Password</h3>
              <button
                class="px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200"
                [class]="isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
              >
                Change Password
              </button>
            </div>

            <!-- Account Status -->
            <div>
              <h3 class="text-sm font-medium mb-3" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Account Status</h3>
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <span class="text-sm" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Active</span>
              </div>
            </div>

            <!-- Last Login -->
            <div>
              <h3 class="text-sm font-medium mb-3" [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">Last Login</h3>
              <p class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-600'">
                Today at 2:30 PM
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end space-x-4">
        <button
          *ngIf="!isEditing"
          (click)="toggleEdit()"
          class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200"
        >
          Edit Profile
        </button>

        <div *ngIf="isEditing" class="flex space-x-4">
          <button
            (click)="toggleEdit()"
            class="px-6 py-3 border rounded-lg font-medium transition-colors duration-200"
            [class]="isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
            [disabled]="isSaving"
          >
            Cancel
          </button>
          <button
            (click)="saveProfile()"
            class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 disabled:opacity-50"
            [disabled]="isSaving"
          >
            {{ isSaving ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
